import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';

// Environment variables validation
const JWT_SECRET = process.env.JWT_SECRET;
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '7d';

if (!JWT_SECRET) {
  throw new Error('JWT_SECRET environment variable is required');
}

// Types
export interface User {
  user_id: string;
  username: string;
  email: string;
  role: 'user' | 'admin';
  created_at: string;
  updated_at: string;
}

export interface JWTPayload {
  user_id: string;
  role: 'user' | 'admin';
  iat?: number;
  exp?: number;
}

export interface AuthResult {
  success: boolean;
  user?: User;
  token?: string;
  error?: string;
}

// Password hashing utilities
export class PasswordUtils {
  private static readonly SALT_ROUNDS = 12; // High security salt rounds

  /**
   * Hash a password using bcrypt with salt
   * @param password - Plain text password
   * @returns Promise<string> - Hashed password
   */
  static async hashPassword(password: string): Promise<string> {
    try {
      const salt = await bcrypt.genSalt(this.SALT_ROUNDS);
      const hash = await bcrypt.hash(password, salt);
      return hash;
    } catch (error) {
      throw new Error('Failed to hash password');
    }
  }

  /**
   * Verify a password against its hash
   * @param password - Plain text password
   * @param hash - Stored password hash
   * @returns Promise<boolean> - True if password matches
   */
  static async verifyPassword(password: string, hash: string): Promise<boolean> {
    try {
      return await bcrypt.compare(password, hash);
    } catch (error) {
      throw new Error('Failed to verify password');
    }
  }
}

// JWT utilities
export class JWTUtils {
  /**
   * Generate a JWT token for a user
   * @param payload - JWT payload containing user_id and role
   * @returns string - Signed JWT token
   */
  static generateToken(payload: Omit<JWTPayload, 'iat' | 'exp'>): string {
    try {
      return jwt.sign(payload, JWT_SECRET!, {
        expiresIn: JWT_EXPIRES_IN,
        algorithm: 'HS256'
      });
    } catch (error) {
      throw new Error('Failed to generate JWT token');
    }
  }

  /**
   * Verify and decode a JWT token
   * @param token - JWT token to verify
   * @returns JWTPayload - Decoded payload
   */
  static verifyToken(token: string): JWTPayload {
    try {
      const decoded = jwt.verify(token, JWT_SECRET!, {
        algorithms: ['HS256']
      }) as JWTPayload;
      return decoded;
    } catch (error) {
      if (error instanceof jwt.TokenExpiredError) {
        throw new Error('Token has expired');
      } else if (error instanceof jwt.JsonWebTokenError) {
        throw new Error('Invalid token');
      } else {
        throw new Error('Token verification failed');
      }
    }
  }

  /**
   * Extract token from Authorization header
   * @param authHeader - Authorization header value
   * @returns string | null - Extracted token or null
   */
  static extractTokenFromHeader(authHeader: string | null): string | null {
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return null;
    }
    return authHeader.substring(7); // Remove 'Bearer ' prefix
  }
}

// Cookie configuration constants
export const COOKIE_CONFIG = {
  NAME: 'haq_auth_token',
  OPTIONS: {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict' as const,
    maxAge: 7 * 24 * 60 * 60, // 7 days in seconds
    path: '/'
  }
};

// Input validation utilities
export class ValidationUtils {
  /**
   * Validate email format
   * @param email - Email to validate
   * @returns boolean - True if valid
   */
  static isValidEmail(email: string): boolean {
    const emailRegex = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;
    return emailRegex.test(email) && email.length <= 255;
  }

  /**
   * Validate username format
   * @param username - Username to validate
   * @returns boolean - True if valid
   */
  static isValidUsername(username: string): boolean {
    // Username: 3-50 characters, alphanumeric and underscores only
    const usernameRegex = /^[a-zA-Z0-9_]{3,50}$/;
    return usernameRegex.test(username);
  }

  /**
   * Validate password strength
   * @param password - Password to validate
   * @returns { isValid: boolean, errors: string[] }
   */
  static validatePassword(password: string): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (password.length < 8) {
      errors.push('Password must be at least 8 characters long');
    }

    if (password.length > 128) {
      errors.push('Password must be less than 128 characters');
    }

    if (!/[a-z]/.test(password)) {
      errors.push('Password must contain at least one lowercase letter');
    }

    if (!/[A-Z]/.test(password)) {
      errors.push('Password must contain at least one uppercase letter');
    }

    if (!/[0-9]/.test(password)) {
      errors.push('Password must contain at least one number');
    }

    if (!/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
      errors.push('Password must contain at least one special character');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Sanitize input string to prevent XSS
   * @param input - Input string to sanitize
   * @returns string - Sanitized string
   */
  static sanitizeInput(input: string): string {
    return input
      .trim()
      .replace(/[<>]/g, '') // Remove potential HTML tags
      .substring(0, 1000); // Limit length
  }
}

// Authentication helper functions (client-side)
export class AuthHelpers {
  /**
   * Verify JWT token and extract payload
   * @param token - JWT token to verify
   * @returns JWTPayload | null - Decoded payload or null if invalid
   */
  static verifyTokenSafe(token: string): JWTPayload | null {
    try {
      return JWTUtils.verifyToken(token);
    } catch (error) {
      return null;
    }
  }

  /**
   * Check if user has admin role from token
   * @param token - JWT token
   * @returns boolean - True if user is admin
   */
  static isAdminFromToken(token: string): boolean {
    try {
      const payload = JWTUtils.verifyToken(token);
      return payload.role === 'admin';
    } catch (error) {
      return false;
    }
  }
}
