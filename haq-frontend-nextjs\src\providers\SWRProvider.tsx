'use client'

import { SWRConfig } from 'swr'
import { createClientComponentClient } from '@/lib/supabase'

// Default fetcher function for SWR
const fetcher = async (url: string) => {
  const supabase = createClientComponentClient()
  
  // Handle different API endpoints
  if (url.startsWith('/api/companies')) {
    const { data: companies, error } = await supabase
      .from('haq_content_db.companies')
      .select(`
        *,
        company_flags:haq_content_db.company_flags(*)
      `)
      .order('haq_score', { ascending: false })

    if (error) throw error

    // Transform data to match our component expectations
    return companies.map(company => ({
      ...company,
      id: company.company_id,
      redFlags: company.company_flags
        ?.filter(flag => flag.flag_type === 'red')
        ?.map(flag => flag.flag_text) || [],
      greenFlags: company.company_flags
        ?.filter(flag => flag.flag_type === 'green')
        ?.map(flag => flag.flag_text) || []
    }))
  }
  
  if (url.startsWith('/api/company/')) {
    const slug = url.split('/').pop()
    const { data, error } = await supabase
      .from('haq_content_db.companies')
      .select(`
        *,
        company_flags:haq_content_db.company_flags(*)
      `)
      .eq('slug', slug)
      .single()

    if (error) throw error

    return {
      ...data,
      id: data.company_id,
      redFlags: data.company_flags
        ?.filter(flag => flag.flag_type === 'red')
        ?.map(flag => flag.flag_text) || [],
      greenFlags: data.company_flags
        ?.filter(flag => flag.flag_type === 'green')
        ?.map(flag => flag.flag_text) || []
    }
  }
  
  // Default fetch for other endpoints
  const response = await fetch(url)
  if (!response.ok) {
    throw new Error('Failed to fetch')
  }
  return response.json()
}

// SWR configuration
const swrConfig = {
  fetcher,
  revalidateOnFocus: false,
  revalidateOnReconnect: true,
  refreshInterval: 0, // Disable automatic refresh
  errorRetryCount: 3,
  errorRetryInterval: 5000,
  onError: (error: Error) => {
    console.error('SWR Error:', error)
  },
  onSuccess: (data: any, key: string) => {
    // Optional: Log successful data fetches in development
    if (process.env.NODE_ENV === 'development') {
      console.log('SWR Success:', key, data)
    }
  }
}

interface SWRProviderProps {
  children: React.ReactNode
}

export function SWRProvider({ children }: SWRProviderProps) {
  return (
    <SWRConfig value={swrConfig}>
      {children}
    </SWRConfig>
  )
}
