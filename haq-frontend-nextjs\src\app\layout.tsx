import type { <PERSON>ada<PERSON> } from "next";
import { Sora } from "next/font/google";
import "./globals.css";
import { Head<PERSON> } from "@/components/layout/Header";
import { Footer } from "@/components/layout/Footer";
import { SWRProvider } from "@/providers/SWRProvider";

const sora = Sora({
  variable: "--font-sora",
  subsets: ["latin"],
  weight: ["300", "400", "500", "600", "700", "800"],
});

export const metadata: Metadata = {
  title: "Haq - Your Right to Workplace Transparency",
  description: "Empowering Pakistani employees through anonymous reviews, salary insights, and workplace accountability. Your voice matters in building a fair workplace culture.",
  keywords: "Pakistan jobs, workplace reviews, salary comparison, employee rights, workplace transparency",
  authors: [{ name: "Haq Platform" }],
  openGraph: {
    title: "Haq - Your Right to Workplace Transparency",
    description: "Empowering Pakistani employees through anonymous reviews and workplace accountability.",
    type: "website",
    locale: "en_US",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className={sora.variable}>
      <body className="min-h-screen bg-background-primary text-text-primary font-sora antialiased flex flex-col">
        <SWRProvider>
          <Header />
          <main className="flex-1">
            {children}
          </main>
          <Footer />
        </SWRProvider>
      </body>
    </html>
  );
}
