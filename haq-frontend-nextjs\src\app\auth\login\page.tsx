'use client';

import React, { useState } from 'react';
import { useForm, SubmitHandler } from 'react-hook-form';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { ValidationUtils } from '@/lib/auth';

// Form data type
interface LoginFormData {
  email: string;
  password: string;
  rememberMe?: boolean;
}

// API response type
interface LoginResponse {
  success: boolean;
  message: string;
  user?: {
    user_id: string;
    username: string;
    email: string;
    role: string;
  };
}

export default function LoginPage() {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [apiError, setApiError] = useState<string | null>(null);
  const [apiSuccess, setApiSuccess] = useState<string | null>(null);

  const {
    register,
    handleSubmit,
    formState: { errors },
    setError,
    clearErrors
  } = useForm<LoginFormData>({
    mode: 'onBlur', // Validate on blur for better UX
  });

  const onSubmit: SubmitHandler<LoginFormData> = async (data) => {
    setIsSubmitting(true);
    setApiError(null);
    setApiSuccess(null);
    clearErrors();

    try {
      // Call login API
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: data.email,
          password: data.password
        }),
      });

      const result: LoginResponse = await response.json();

      if (result.success) {
        setApiSuccess('Login successful! Redirecting...');
        
        // Redirect to dashboard or home page after successful login
        setTimeout(() => {
          router.push('/');
        }, 1500);
      } else {
        setApiError(result.message || 'Login failed. Please try again.');
        
        // Handle specific field errors from server
        if (result.message.includes('email') || result.message.includes('password')) {
          setError('email', {
            type: 'manual',
            message: 'Invalid email or password'
          });
          setError('password', {
            type: 'manual',
            message: 'Invalid email or password'
          });
        }
      }
    } catch (error) {
      console.error('Login error:', error);
      setApiError('Network error. Please check your connection and try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-background-primary flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        {/* Header */}
        <div className="text-center">
          <div className="flex justify-center mb-6">
            <div className="w-12 h-12 bg-gradient-to-br from-accent-primary to-accent-secondary rounded-xl flex items-center justify-center">
              <span className="text-white font-bold text-xl">H</span>
            </div>
          </div>
          <h2 className="text-3xl font-bold text-text-primary">
            Sign in to your account
          </h2>
          <p className="mt-2 text-sm text-text-secondary">
            Welcome back to HAQ
          </p>
        </div>

        {/* Success Message */}
        {apiSuccess && (
          <div className="bg-green-50 border border-green-200 rounded-medium p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-green-800">{apiSuccess}</p>
              </div>
            </div>
          </div>
        )}

        {/* Error Message */}
        {apiError && (
          <div className="bg-red-50 border border-red-200 rounded-medium p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-red-800">{apiError}</p>
              </div>
            </div>
          </div>
        )}

        {/* Login Form */}
        <form className="mt-8 space-y-6" onSubmit={handleSubmit(onSubmit)}>
          <div className="space-y-4">
            {/* Email Field */}
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-text-primary mb-2">
                Email address
              </label>
              <input
                id="email"
                type="email"
                autoComplete="email"
                aria-invalid={errors.email ? 'true' : 'false'}
                className={`
                  appearance-none relative block w-full px-3 py-3 border rounded-medium placeholder-text-secondary text-text-primary 
                  focus:outline-none focus:ring-2 focus:ring-accent-primary focus:border-accent-primary focus:z-10 sm:text-sm
                  ${errors.email 
                    ? 'border-red-300 bg-red-50' 
                    : 'border-border-primary bg-surface-primary hover:border-border-secondary'
                  }
                `}
                placeholder="Enter your email"
                {...register('email', {
                  required: 'Email is required',
                  maxLength: {
                    value: 255,
                    message: 'Email must be less than 255 characters'
                  },
                  pattern: {
                    value: /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/,
                    message: 'Please enter a valid email address'
                  },
                  validate: (value) => {
                    if (!ValidationUtils.isValidEmail(value)) {
                      return 'Please enter a valid email address';
                    }
                    return true;
                  }
                })}
              />
              {errors.email && (
                <p className="mt-1 text-sm text-red-600" role="alert">
                  {errors.email.message}
                </p>
              )}
            </div>

            {/* Password Field */}
            <div>
              <label htmlFor="password" className="block text-sm font-medium text-text-primary mb-2">
                Password
              </label>
              <input
                id="password"
                type="password"
                autoComplete="current-password"
                aria-invalid={errors.password ? 'true' : 'false'}
                className={`
                  appearance-none relative block w-full px-3 py-3 border rounded-medium placeholder-text-secondary text-text-primary 
                  focus:outline-none focus:ring-2 focus:ring-accent-primary focus:border-accent-primary focus:z-10 sm:text-sm
                  ${errors.password 
                    ? 'border-red-300 bg-red-50' 
                    : 'border-border-primary bg-surface-primary hover:border-border-secondary'
                  }
                `}
                placeholder="Enter your password"
                {...register('password', {
                  required: 'Password is required',
                  minLength: {
                    value: 1,
                    message: 'Password is required'
                  }
                })}
              />
              {errors.password && (
                <p className="mt-1 text-sm text-red-600" role="alert">
                  {errors.password.message}
                </p>
              )}
            </div>
          </div>

          {/* Remember Me and Forgot Password */}
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <input
                id="rememberMe"
                type="checkbox"
                className="h-4 w-4 text-accent-primary focus:ring-accent-primary border-border-primary rounded"
                {...register('rememberMe')}
              />
              <label htmlFor="rememberMe" className="ml-2 block text-sm text-text-secondary">
                Remember me
              </label>
            </div>

            <div className="text-sm">
              <Link 
                href="/auth/forgot-password" 
                className="font-medium text-accent-primary hover:text-accent-secondary transition-colors"
              >
                Forgot your password?
              </Link>
            </div>
          </div>

          {/* Submit Button */}
          <div>
            <button
              type="submit"
              disabled={isSubmitting}
              className={`
                group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-medium text-white 
                focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-accent-primary
                ${isSubmitting 
                  ? 'bg-gray-400 cursor-not-allowed' 
                  : 'bg-accent-primary hover:bg-accent-secondary'
                }
              `}
            >
              {isSubmitting ? (
                <>
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Signing in...
                </>
              ) : (
                'Sign in'
              )}
            </button>
          </div>

          {/* Sign Up Link */}
          <div className="text-center">
            <p className="text-sm text-text-secondary">
              Don't have an account?{' '}
              <Link 
                href="/auth/signup" 
                className="font-medium text-accent-primary hover:text-accent-secondary transition-colors"
              >
                Create one now
              </Link>
            </p>
          </div>
        </form>

        {/* Additional Security Notice */}
        <div className="mt-6 text-center">
          <p className="text-xs text-text-secondary">
            By signing in, you agree to our{' '}
            <Link href="/privacy" className="text-accent-primary hover:text-accent-secondary">
              Privacy Policy
            </Link>{' '}
            and{' '}
            <Link href="/terms" className="text-accent-primary hover:text-accent-secondary">
              Terms of Service
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
}
