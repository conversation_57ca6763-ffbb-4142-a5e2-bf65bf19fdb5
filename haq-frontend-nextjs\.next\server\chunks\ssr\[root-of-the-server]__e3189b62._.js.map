{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Haq%20website%20v1/haq-frontend-nextjs/src/components/layout/Header.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState } from 'react';\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport { Search, Menu, X, Shield, Users, DollarSign, MessageCircle } from 'lucide-react';\n\nexport const Header: React.FC = () => {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const pathname = usePathname();\n\n  const navigation = [\n    { name: 'Companies', href: '/companies', icon: Shield },\n    { name: 'Salaries', href: '/salaries', icon: DollarSign },\n    { name: 'Community', href: '/community', icon: MessageCircle },\n  ];\n\n  return (\n    <header className=\"bg-background-primary border-b border-border-primary sticky top-0 z-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center h-16\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center space-x-2 group\">\n            <div className=\"w-8 h-8 bg-gradient-to-br from-accent-primary to-accent-secondary rounded-lg flex items-center justify-center group-hover:scale-105 transition-transform duration-200\">\n              <Shield className=\"w-5 h-5 text-text-on-accent\" />\n            </div>\n            <div className=\"flex flex-col\">\n              <span className=\"text-xl font-bold text-accent-primary\">Haq</span>\n              <span className=\"text-xs text-text-secondary -mt-1\">حق</span>\n            </div>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden md:flex items-center space-x-8\">\n            {navigation.map((item) => {\n              const Icon = item.icon;\n              const isActive = pathname === item.href;\n              return (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className={`flex items-center space-x-1 px-3 py-2 rounded-lg text-navigation-link font-medium transition-all duration-200 uppercase tracking-wide ${\n                    isActive\n                      ? 'text-accent-primary bg-surface-secondary'\n                      : 'text-text-secondary hover:text-accent-primary hover:bg-surface-primary'\n                  }`}\n                >\n                  <Icon className=\"w-4 h-4\" />\n                  <span>{item.name}</span>\n                </Link>\n              );\n            })}\n          </nav>\n\n          {/* Action Buttons */}\n          <div className=\"hidden md:flex items-center space-x-4\">\n            <Link\n              href=\"/review/submit\"\n              className=\"bg-accent-primary hover:bg-accent-secondary text-text-on-accent px-4 py-2 rounded-lg text-sm font-semibold transition-all duration-200 flex items-center space-x-2 hover:shadow-glow transform hover:-translate-y-0.5\"\n            >\n              <Users className=\"w-4 h-4\" />\n              <span>Write Review</span>\n            </Link>\n          </div>\n\n          {/* Mobile menu button */}\n          <button\n            onClick={() => setIsMenuOpen(!isMenuOpen)}\n            className=\"md:hidden p-2 rounded-lg text-text-secondary hover:text-accent-primary hover:bg-surface-primary transition-colors duration-200\"\n          >\n            {isMenuOpen ? <X className=\"w-6 h-6\" /> : <Menu className=\"w-6 h-6\" />}\n          </button>\n        </div>\n      </div>\n\n      {/* Mobile Navigation */}\n      {isMenuOpen && (\n        <div className=\"md:hidden bg-surface-primary border-t border-border-primary\">\n          <div className=\"px-4 py-3 space-y-3\">\n            {navigation.map((item) => {\n              const Icon = item.icon;\n              const isActive = pathname === item.href;\n              return (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  onClick={() => setIsMenuOpen(false)}\n                  className={`flex items-center space-x-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors duration-200 ${\n                    isActive\n                      ? 'text-accent-primary bg-surface-secondary'\n                      : 'text-text-secondary hover:text-accent-primary hover:bg-surface-secondary'\n                  }`}\n                >\n                  <Icon className=\"w-5 h-5\" />\n                  <span>{item.name}</span>\n                </Link>\n              );\n            })}\n            <Link\n              href=\"/review/submit\"\n              onClick={() => setIsMenuOpen(false)}\n              className=\"flex items-center space-x-3 bg-accent-primary hover:bg-accent-secondary text-text-on-accent px-3 py-2 rounded-lg text-sm font-medium transition-colors duration-200\"\n            >\n              <Users className=\"w-5 h-5\" />\n              <span>Write Review</span>\n            </Link>\n          </div>\n        </div>\n      )}\n    </header>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;;AAOO,MAAM,SAAmB;IAC9B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,aAAa;QACjB;YAAE,MAAM;YAAa,MAAM;YAAc,MAAM,sMAAA,CAAA,SAAM;QAAC;QACtD;YAAE,MAAM;YAAY,MAAM;YAAa,MAAM,kNAAA,CAAA,aAAU;QAAC;QACxD;YAAE,MAAM;YAAa,MAAM;YAAc,MAAM,wNAAA,CAAA,gBAAa;QAAC;KAC9D;IAED,qBACE,8OAAC;QAAO,WAAU;;0BAChB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;;;;;;8CAEpB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAwC;;;;;;sDACxD,8OAAC;4CAAK,WAAU;sDAAoC;;;;;;;;;;;;;;;;;;sCAKxD,8OAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC;gCACf,MAAM,OAAO,KAAK,IAAI;gCACtB,MAAM,WAAW,aAAa,KAAK,IAAI;gCACvC,qBACE,8OAAC,4JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAW,CAAC,sIAAsI,EAChJ,WACI,6CACA,0EACJ;;sDAEF,8OAAC;4CAAK,WAAU;;;;;;sDAChB,8OAAC;sDAAM,KAAK,IAAI;;;;;;;mCATX,KAAK,IAAI;;;;;4BAYpB;;;;;;sCAIF,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,8OAAC;kDAAK;;;;;;;;;;;;;;;;;sCAKV,8OAAC;4BACC,SAAS,IAAM,cAAc,CAAC;4BAC9B,WAAU;sCAET,2BAAa,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;qDAAe,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;YAM/D,4BACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;wBACZ,WAAW,GAAG,CAAC,CAAC;4BACf,MAAM,OAAO,KAAK,IAAI;4BACtB,MAAM,WAAW,aAAa,KAAK,IAAI;4BACvC,qBACE,8OAAC,4JAAA,CAAA,UAAI;gCAEH,MAAM,KAAK,IAAI;gCACf,SAAS,IAAM,cAAc;gCAC7B,WAAW,CAAC,oGAAoG,EAC9G,WACI,6CACA,4EACJ;;kDAEF,8OAAC;wCAAK,WAAU;;;;;;kDAChB,8OAAC;kDAAM,KAAK,IAAI;;;;;;;+BAVX,KAAK,IAAI;;;;;wBAapB;sCACA,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,SAAS,IAAM,cAAc;4BAC7B,WAAU;;8CAEV,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;8CACjB,8OAAC;8CAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpB", "debugId": null}}, {"offset": {"line": 412, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Haq%20website%20v1/haq-frontend-nextjs/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\nimport { createBrowserClient, createServerClient } from '@supabase/ssr'\nimport { cookies } from 'next/headers'\n\n// Supabase configuration\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n\n// Client-side Supabase client\nexport const createClientComponentClient = () => {\n  return createBrowserClient(supabaseUrl, supabaseAnonKey)\n}\n\n// Server-side Supabase client for Server Components\nexport const createServerComponentClient = async () => {\n  const cookieStore = await cookies()\n  \n  return createServerClient(supabaseUrl, supabaseAnonKey, {\n    cookies: {\n      getAll() {\n        return cookieStore.getAll()\n      },\n      setAll(cookiesToSet) {\n        try {\n          cookiesToSet.forEach(({ name, value, options }) =>\n            cookieStore.set(name, value, options)\n          )\n        } catch {\n          // The `setAll` method was called from a Server Component.\n          // This can be ignored if you have middleware refreshing\n          // user sessions.\n        }\n      },\n    },\n  })\n}\n\n// Server-side Supabase client for Route Handlers\nexport const createRouteHandlerClient = async () => {\n  const cookieStore = await cookies()\n  \n  return createServerClient(supabaseUrl, supabaseAnonKey, {\n    cookies: {\n      getAll() {\n        return cookieStore.getAll()\n      },\n      setAll(cookiesToSet) {\n        try {\n          cookiesToSet.forEach(({ name, value, options }) =>\n            cookieStore.set(name, value, options)\n          )\n        } catch {\n          // The `setAll` method was called from a Route Handler.\n          // This can be ignored if you have middleware refreshing\n          // user sessions.\n        }\n      },\n    },\n  })\n}\n\n// Legacy client for backward compatibility\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey)\n\n// Database types (will be generated later)\nexport type Database = {\n  haq_users_db: {\n    Tables: {\n      users: {\n        Row: {\n          user_id: string\n          username: string\n          email: string\n          password_hash: string\n          role: 'user' | 'admin'\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          user_id?: string\n          username: string\n          email: string\n          password_hash: string\n          role?: 'user' | 'admin'\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          user_id?: string\n          username?: string\n          email?: string\n          password_hash?: string\n          role?: 'user' | 'admin'\n          created_at?: string\n          updated_at?: string\n        }\n      }\n    }\n  }\n  haq_content_db: {\n    Tables: {\n      companies: {\n        Row: {\n          company_id: string\n          name: string\n          slug: string\n          industry: string | null\n          location: string | null\n          description: string | null\n          website_url: string | null\n          logo_url: string | null\n          employee_count_range: string | null\n          founded_year: number | null\n          haq_score: number\n          total_reviews: number\n          is_verified: boolean\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          company_id?: string\n          name: string\n          slug: string\n          industry?: string | null\n          location?: string | null\n          description?: string | null\n          website_url?: string | null\n          logo_url?: string | null\n          employee_count_range?: string | null\n          founded_year?: number | null\n          haq_score?: number\n          total_reviews?: number\n          is_verified?: boolean\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          company_id?: string\n          name?: string\n          slug?: string\n          industry?: string | null\n          location?: string | null\n          description?: string | null\n          website_url?: string | null\n          logo_url?: string | null\n          employee_count_range?: string | null\n          founded_year?: number | null\n          haq_score?: number\n          total_reviews?: number\n          is_verified?: boolean\n          created_at?: string\n          updated_at?: string\n        }\n      }\n      reviews: {\n        Row: {\n          review_id: string\n          company_id: string\n          anonymous_user_hash: string\n          overall_rating: number | null\n          work_life_balance_rating: number | null\n          compensation_rating: number | null\n          management_rating: number | null\n          culture_rating: number | null\n          title: string\n          pros: string | null\n          cons: string | null\n          advice_to_management: string | null\n          job_title: string | null\n          employment_status: 'current' | 'former' | null\n          employment_duration: string | null\n          department: string | null\n          location: string | null\n          is_approved: boolean\n          is_featured: boolean\n          helpful_count: number\n          created_at: string\n          updated_at: string\n        }\n      }\n      salary_reports: {\n        Row: {\n          salary_id: string\n          company_id: string\n          anonymous_user_hash: string\n          job_title: string\n          department: string | null\n          location: string | null\n          experience_level: 'entry' | 'mid' | 'senior' | 'lead' | 'executive' | null\n          base_salary: number | null\n          bonus: number\n          stock_options: number\n          total_compensation: number | null\n          currency: string\n          employment_type: 'full-time' | 'part-time' | 'contract' | 'internship' | null\n          years_of_experience: number | null\n          years_at_company: number | null\n          is_approved: boolean\n          created_at: string\n          updated_at: string\n        }\n      }\n      company_flags: {\n        Row: {\n          flag_id: string\n          company_id: string\n          flag_type: 'red' | 'green' | null\n          flag_text: string\n          flag_count: number\n          created_at: string\n        }\n      }\n    }\n  }\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AAAA;AAAA;AACA;;;;AAEA,yBAAyB;AACzB,MAAM;AACN,MAAM;AAGC,MAAM,8BAA8B;IACzC,OAAO,CAAA,GAAA,0KAAA,CAAA,sBAAmB,AAAD,EAAE,aAAa;AAC1C;AAGO,MAAM,8BAA8B;IACzC,MAAM,cAAc,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAEhC,OAAO,CAAA,GAAA,yKAAA,CAAA,qBAAkB,AAAD,EAAE,aAAa,iBAAiB;QACtD,SAAS;YACP;gBACE,OAAO,YAAY,MAAM;YAC3B;YACA,QAAO,YAAY;gBACjB,IAAI;oBACF,aAAa,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,GAC5C,YAAY,GAAG,CAAC,MAAM,OAAO;gBAEjC,EAAE,OAAM;gBACN,0DAA0D;gBAC1D,wDAAwD;gBACxD,iBAAiB;gBACnB;YACF;QACF;IACF;AACF;AAGO,MAAM,2BAA2B;IACtC,MAAM,cAAc,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAEhC,OAAO,CAAA,GAAA,yKAAA,CAAA,qBAAkB,AAAD,EAAE,aAAa,iBAAiB;QACtD,SAAS;YACP;gBACE,OAAO,YAAY,MAAM;YAC3B;YACA,QAAO,YAAY;gBACjB,IAAI;oBACF,aAAa,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,GAC5C,YAAY,GAAG,CAAC,MAAM,OAAO;gBAEjC,EAAE,OAAM;gBACN,uDAAuD;gBACvD,wDAAwD;gBACxD,iBAAiB;gBACnB;YACF;QACF;IACF;AACF;AAGO,MAAM,WAAW,CAAA,GAAA,uLAAA,CAAA,eAAY,AAAD,EAAE,aAAa", "debugId": null}}, {"offset": {"line": 477, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Haq%20website%20v1/haq-frontend-nextjs/src/providers/SWRProvider.tsx"], "sourcesContent": ["'use client'\n\nimport { SWRConfig } from 'swr'\nimport { createClientComponentClient } from '@/lib/supabase'\n\n// Default fetcher function for SWR\nconst fetcher = async (url: string) => {\n  const supabase = createClientComponentClient()\n  \n  // Handle different API endpoints\n  if (url.startsWith('/api/companies')) {\n    const { data, error } = await supabase\n      .from('haq_content_db.companies')\n      .select(`\n        *,\n        company_flags:haq_content_db.company_flags(*)\n      `)\n      .order('haq_score', { ascending: false })\n    \n    if (error) throw error\n    \n    // Transform data to match our component expectations\n    return data.map(company => ({\n      ...company,\n      id: company.company_id,\n      redFlags: company.company_flags\n        ?.filter(flag => flag.flag_type === 'red')\n        ?.map(flag => flag.flag_text) || [],\n      greenFlags: company.company_flags\n        ?.filter(flag => flag.flag_type === 'green')\n        ?.map(flag => flag.flag_text) || []\n    }))\n  }\n  \n  if (url.startsWith('/api/company/')) {\n    const slug = url.split('/').pop()\n    const { data, error } = await supabase\n      .from('haq_content_db.companies')\n      .select(`\n        *,\n        company_flags:haq_content_db.company_flags(*),\n        reviews:haq_content_db.reviews(*),\n        salary_reports:haq_content_db.salary_reports(*)\n      `)\n      .eq('slug', slug)\n      .single()\n    \n    if (error) throw error\n    \n    return {\n      ...data,\n      id: data.company_id,\n      redFlags: data.company_flags\n        ?.filter(flag => flag.flag_type === 'red')\n        ?.map(flag => flag.flag_text) || [],\n      greenFlags: data.company_flags\n        ?.filter(flag => flag.flag_type === 'green')\n        ?.map(flag => flag.flag_text) || []\n    }\n  }\n  \n  // Default fetch for other endpoints\n  const response = await fetch(url)\n  if (!response.ok) {\n    throw new Error('Failed to fetch')\n  }\n  return response.json()\n}\n\n// SWR configuration\nconst swrConfig = {\n  fetcher,\n  revalidateOnFocus: false,\n  revalidateOnReconnect: true,\n  refreshInterval: 0, // Disable automatic refresh\n  errorRetryCount: 3,\n  errorRetryInterval: 5000,\n  onError: (error: Error) => {\n    console.error('SWR Error:', error)\n  },\n  onSuccess: (data: any, key: string) => {\n    // Optional: Log successful data fetches in development\n    if (process.env.NODE_ENV === 'development') {\n      console.log('SWR Success:', key, data)\n    }\n  }\n}\n\ninterface SWRProviderProps {\n  children: React.ReactNode\n}\n\nexport function SWRProvider({ children }: SWRProviderProps) {\n  return (\n    <SWRConfig value={swrConfig}>\n      {children}\n    </SWRConfig>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKA,mCAAmC;AACnC,MAAM,UAAU,OAAO;IACrB,MAAM,WAAW,CAAA,GAAA,sHAAA,CAAA,8BAA2B,AAAD;IAE3C,iCAAiC;IACjC,IAAI,IAAI,UAAU,CAAC,mBAAmB;QACpC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,4BACL,MAAM,CAAC,CAAC;;;MAGT,CAAC,EACA,KAAK,CAAC,aAAa;YAAE,WAAW;QAAM;QAEzC,IAAI,OAAO,MAAM;QAEjB,qDAAqD;QACrD,OAAO,KAAK,GAAG,CAAC,CAAA,UAAW,CAAC;gBAC1B,GAAG,OAAO;gBACV,IAAI,QAAQ,UAAU;gBACtB,UAAU,QAAQ,aAAa,EAC3B,OAAO,CAAA,OAAQ,KAAK,SAAS,KAAK,QAClC,IAAI,CAAA,OAAQ,KAAK,SAAS,KAAK,EAAE;gBACrC,YAAY,QAAQ,aAAa,EAC7B,OAAO,CAAA,OAAQ,KAAK,SAAS,KAAK,UAClC,IAAI,CAAA,OAAQ,KAAK,SAAS,KAAK,EAAE;YACvC,CAAC;IACH;IAEA,IAAI,IAAI,UAAU,CAAC,kBAAkB;QACnC,MAAM,OAAO,IAAI,KAAK,CAAC,KAAK,GAAG;QAC/B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,4BACL,MAAM,CAAC,CAAC;;;;;MAKT,CAAC,EACA,EAAE,CAAC,QAAQ,MACX,MAAM;QAET,IAAI,OAAO,MAAM;QAEjB,OAAO;YACL,GAAG,IAAI;YACP,IAAI,KAAK,UAAU;YACnB,UAAU,KAAK,aAAa,EACxB,OAAO,CAAA,OAAQ,KAAK,SAAS,KAAK,QAClC,IAAI,CAAA,OAAQ,KAAK,SAAS,KAAK,EAAE;YACrC,YAAY,KAAK,aAAa,EAC1B,OAAO,CAAA,OAAQ,KAAK,SAAS,KAAK,UAClC,IAAI,CAAA,OAAQ,KAAK,SAAS,KAAK,EAAE;QACvC;IACF;IAEA,oCAAoC;IACpC,MAAM,WAAW,MAAM,MAAM;IAC7B,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO,SAAS,IAAI;AACtB;AAEA,oBAAoB;AACpB,MAAM,YAAY;IAChB;IACA,mBAAmB;IACnB,uBAAuB;IACvB,iBAAiB;IACjB,iBAAiB;IACjB,oBAAoB;IACpB,SAAS,CAAC;QACR,QAAQ,KAAK,CAAC,cAAc;IAC9B;IACA,WAAW,CAAC,MAAW;QACrB,uDAAuD;QACvD,wCAA4C;YAC1C,QAAQ,GAAG,CAAC,gBAAgB,KAAK;QACnC;IACF;AACF;AAMO,SAAS,YAAY,EAAE,QAAQ,EAAoB;IACxD,qBACE,8OAAC,8JAAA,CAAA,YAAS;QAAC,OAAO;kBACf;;;;;;AAGP", "debugId": null}}]}