[{"name": "hot-reloader", "duration": 192, "timestamp": 194376679235, "id": 3, "tags": {"version": "15.3.4"}, "startTime": 1750563192125, "traceId": "0428be4466555269"}, {"name": "setup-dev-bundler", "duration": 1284329, "timestamp": 194376262104, "id": 2, "parentId": 1, "tags": {}, "startTime": 1750563191707, "traceId": "0428be4466555269"}, {"name": "run-instrumentation-hook", "duration": 24, "timestamp": 194377669638, "id": 4, "parentId": 1, "tags": {}, "startTime": 1750563193115, "traceId": "0428be4466555269"}, {"name": "start-dev-server", "duration": 2664369, "timestamp": 194375027060, "id": 1, "tags": {"cpus": "8", "platform": "win32", "memory.freeMem": "919732224", "memory.totalMem": "8215093248", "memory.heapSizeLimit": "4157603840", "memory.rss": "180047872", "memory.heapTotal": "98750464", "memory.heapUsed": "71708936"}, "startTime": 1750563190472, "traceId": "0428be4466555269"}, {"name": "compile-path", "duration": 4634184, "timestamp": 194379929162, "id": 7, "tags": {"trigger": "/"}, "startTime": 1750563195374, "traceId": "0428be4466555269"}, {"name": "ensure-page", "duration": 4635926, "timestamp": 194379928396, "id": 6, "parentId": 3, "tags": {"inputPage": "/page"}, "startTime": 1750563195374, "traceId": "0428be4466555269"}]