import { NextRequest, NextResponse } from 'next/server';
import { <PERSON><PERSON>Utils, JWTUtils } from '@/lib/auth';

interface LogoutResponse {
  success: boolean;
  message: string;
}

export async function POST(request: NextRequest): Promise<NextResponse<LogoutResponse>> {
  try {
    // Get current token for logging purposes (optional)
    const token = await CookieUtils.getAuthToken();
    
    if (token) {
      try {
        const payload = JWTUtils.verifyToken(token);
        console.info(`User logout: ${payload.user_id}`);
      } catch (error) {
        // Token might be invalid/expired, but we still want to clear the cookie
        console.warn('Invalid token during logout, clearing cookie anyway');
      }
    }

    // Remove authentication cookie
    await <PERSON>ieUtils.removeAuthCookie();

    return NextResponse.json(
      {
        success: true,
        message: 'Logout successful'
      },
      { status: 200 }
    );

  } catch (error) {
    console.error('Logout error:', error);
    
    // Even if there's an error, try to clear the cookie
    try {
      await <PERSON><PERSON><PERSON><PERSON><PERSON>.removeAuthCookie();
    } catch (cookieError) {
      console.error('Failed to clear auth cookie:', cookieError);
    }

    return NextResponse.json(
      { success: false, message: 'Logout failed' },
      { status: 500 }
    );
  }
}

// Handle unsupported methods
export async function GET() {
  return NextResponse.json(
    { success: false, message: 'Method not allowed' },
    { status: 405 }
  );
}

export async function PUT() {
  return NextResponse.json(
    { success: false, message: 'Method not allowed' },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { success: false, message: 'Method not allowed' },
    { status: 405 }
  );
}
