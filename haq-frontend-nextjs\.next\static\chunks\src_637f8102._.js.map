{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Haq%20website%20v1/haq-frontend-nextjs/src/lib/auth.ts"], "sourcesContent": ["import bcrypt from 'bcryptjs';\nimport jwt from 'jsonwebtoken';\n\n// Environment variables validation\nconst JWT_SECRET = process.env.JWT_SECRET;\nconst JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '7d';\n\nif (!JWT_SECRET) {\n  throw new Error('JWT_SECRET environment variable is required');\n}\n\n// Types\nexport interface User {\n  user_id: string;\n  username: string;\n  email: string;\n  role: 'user' | 'admin';\n  created_at: string;\n  updated_at: string;\n}\n\nexport interface JWTPayload {\n  user_id: string;\n  role: 'user' | 'admin';\n  iat?: number;\n  exp?: number;\n}\n\nexport interface AuthResult {\n  success: boolean;\n  user?: User;\n  token?: string;\n  error?: string;\n}\n\n// Password hashing utilities\nexport class PasswordUtils {\n  private static readonly SALT_ROUNDS = 12; // High security salt rounds\n\n  /**\n   * Hash a password using bcrypt with salt\n   * @param password - Plain text password\n   * @returns Promise<string> - Hashed password\n   */\n  static async hashPassword(password: string): Promise<string> {\n    try {\n      const salt = await bcrypt.genSalt(this.SALT_ROUNDS);\n      const hash = await bcrypt.hash(password, salt);\n      return hash;\n    } catch (error) {\n      throw new Error('Failed to hash password');\n    }\n  }\n\n  /**\n   * Verify a password against its hash\n   * @param password - Plain text password\n   * @param hash - Stored password hash\n   * @returns Promise<boolean> - True if password matches\n   */\n  static async verifyPassword(password: string, hash: string): Promise<boolean> {\n    try {\n      return await bcrypt.compare(password, hash);\n    } catch (error) {\n      throw new Error('Failed to verify password');\n    }\n  }\n}\n\n// JWT utilities\nexport class JWTUtils {\n  /**\n   * Generate a JWT token for a user\n   * @param payload - JWT payload containing user_id and role\n   * @returns string - Signed JWT token\n   */\n  static generateToken(payload: Omit<JWTPayload, 'iat' | 'exp'>): string {\n    try {\n      return jwt.sign(payload, JWT_SECRET!, {\n        expiresIn: JWT_EXPIRES_IN,\n        algorithm: 'HS256'\n      });\n    } catch (error) {\n      throw new Error('Failed to generate JWT token');\n    }\n  }\n\n  /**\n   * Verify and decode a JWT token\n   * @param token - JWT token to verify\n   * @returns JWTPayload - Decoded payload\n   */\n  static verifyToken(token: string): JWTPayload {\n    try {\n      const decoded = jwt.verify(token, JWT_SECRET!, {\n        algorithms: ['HS256']\n      }) as JWTPayload;\n      return decoded;\n    } catch (error) {\n      if (error instanceof jwt.TokenExpiredError) {\n        throw new Error('Token has expired');\n      } else if (error instanceof jwt.JsonWebTokenError) {\n        throw new Error('Invalid token');\n      } else {\n        throw new Error('Token verification failed');\n      }\n    }\n  }\n\n  /**\n   * Extract token from Authorization header\n   * @param authHeader - Authorization header value\n   * @returns string | null - Extracted token or null\n   */\n  static extractTokenFromHeader(authHeader: string | null): string | null {\n    if (!authHeader || !authHeader.startsWith('Bearer ')) {\n      return null;\n    }\n    return authHeader.substring(7); // Remove 'Bearer ' prefix\n  }\n}\n\n// Cookie configuration constants\nexport const COOKIE_CONFIG = {\n  NAME: 'haq_auth_token',\n  OPTIONS: {\n    httpOnly: true,\n    secure: process.env.NODE_ENV === 'production',\n    sameSite: 'strict' as const,\n    maxAge: 7 * 24 * 60 * 60, // 7 days in seconds\n    path: '/'\n  }\n};\n\n// Input validation utilities\nexport class ValidationUtils {\n  /**\n   * Validate email format\n   * @param email - Email to validate\n   * @returns boolean - True if valid\n   */\n  static isValidEmail(email: string): boolean {\n    const emailRegex = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;\n    return emailRegex.test(email) && email.length <= 255;\n  }\n\n  /**\n   * Validate username format\n   * @param username - Username to validate\n   * @returns boolean - True if valid\n   */\n  static isValidUsername(username: string): boolean {\n    // Username: 3-50 characters, alphanumeric and underscores only\n    const usernameRegex = /^[a-zA-Z0-9_]{3,50}$/;\n    return usernameRegex.test(username);\n  }\n\n  /**\n   * Validate password strength\n   * @param password - Password to validate\n   * @returns { isValid: boolean, errors: string[] }\n   */\n  static validatePassword(password: string): { isValid: boolean; errors: string[] } {\n    const errors: string[] = [];\n\n    if (password.length < 8) {\n      errors.push('Password must be at least 8 characters long');\n    }\n\n    if (password.length > 128) {\n      errors.push('Password must be less than 128 characters');\n    }\n\n    if (!/[a-z]/.test(password)) {\n      errors.push('Password must contain at least one lowercase letter');\n    }\n\n    if (!/[A-Z]/.test(password)) {\n      errors.push('Password must contain at least one uppercase letter');\n    }\n\n    if (!/[0-9]/.test(password)) {\n      errors.push('Password must contain at least one number');\n    }\n\n    if (!/[!@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>\\/?]/.test(password)) {\n      errors.push('Password must contain at least one special character');\n    }\n\n    return {\n      isValid: errors.length === 0,\n      errors\n    };\n  }\n\n  /**\n   * Sanitize input string to prevent XSS\n   * @param input - Input string to sanitize\n   * @returns string - Sanitized string\n   */\n  static sanitizeInput(input: string): string {\n    return input\n      .trim()\n      .replace(/[<>]/g, '') // Remove potential HTML tags\n      .substring(0, 1000); // Limit length\n  }\n}\n\n// Authentication helper functions (client-side)\nexport class AuthHelpers {\n  /**\n   * Verify JWT token and extract payload\n   * @param token - JWT token to verify\n   * @returns JWTPayload | null - Decoded payload or null if invalid\n   */\n  static verifyTokenSafe(token: string): JWTPayload | null {\n    try {\n      return JWTUtils.verifyToken(token);\n    } catch (error) {\n      return null;\n    }\n  }\n\n  /**\n   * Check if user has admin role from token\n   * @param token - JWT token\n   * @returns boolean - True if user is admin\n   */\n  static isAdminFromToken(token: string): boolean {\n    try {\n      const payload = JWTUtils.verifyToken(token);\n      return payload.role === 'admin';\n    } catch (error) {\n      return false;\n    }\n  }\n}\n"], "names": [], "mappings": ";;;;;;;AAImB;AAJnB;AACA;;;AAEA,mCAAmC;AACnC,MAAM,aAAa,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,UAAU;AACzC,MAAM,iBAAiB,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,cAAc,IAAI;AAErD,IAAI,CAAC,YAAY;IACf,MAAM,IAAI,MAAM;AAClB;AA2BO,MAAM;IACX,OAAwB,cAAc,GAAG;IAEzC;;;;GAIC,GACD,aAAa,aAAa,QAAgB,EAAmB;QAC3D,IAAI;YACF,MAAM,OAAO,MAAM,oIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW;YAClD,MAAM,OAAO,MAAM,oIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,UAAU;YACzC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,MAAM,IAAI,MAAM;QAClB;IACF;IAEA;;;;;GAKC,GACD,aAAa,eAAe,QAAgB,EAAE,IAAY,EAAoB;QAC5E,IAAI;YACF,OAAO,MAAM,oIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,UAAU;QACxC,EAAE,OAAO,OAAO;YACd,MAAM,IAAI,MAAM;QAClB;IACF;AACF;AAGO,MAAM;IACX;;;;GAIC,GACD,OAAO,cAAc,OAAwC,EAAU;QACrE,IAAI;YACF,OAAO,wIAAA,CAAA,UAAG,CAAC,IAAI,CAAC,SAAS,YAAa;gBACpC,WAAW;gBACX,WAAW;YACb;QACF,EAAE,OAAO,OAAO;YACd,MAAM,IAAI,MAAM;QAClB;IACF;IAEA;;;;GAIC,GACD,OAAO,YAAY,KAAa,EAAc;QAC5C,IAAI;YACF,MAAM,UAAU,wIAAA,CAAA,UAAG,CAAC,MAAM,CAAC,OAAO,YAAa;gBAC7C,YAAY;oBAAC;iBAAQ;YACvB;YACA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,IAAI,iBAAiB,wIAAA,CAAA,UAAG,CAAC,iBAAiB,EAAE;gBAC1C,MAAM,IAAI,MAAM;YAClB,OAAO,IAAI,iBAAiB,wIAAA,CAAA,UAAG,CAAC,iBAAiB,EAAE;gBACjD,MAAM,IAAI,MAAM;YAClB,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF;IACF;IAEA;;;;GAIC,GACD,OAAO,uBAAuB,UAAyB,EAAiB;QACtE,IAAI,CAAC,cAAc,CAAC,WAAW,UAAU,CAAC,YAAY;YACpD,OAAO;QACT;QACA,OAAO,WAAW,SAAS,CAAC,IAAI,0BAA0B;IAC5D;AACF;AAGO,MAAM,gBAAgB;IAC3B,MAAM;IACN,SAAS;QACP,UAAU;QACV,QAAQ,oDAAyB;QACjC,UAAU;QACV,QAAQ,IAAI,KAAK,KAAK;QACtB,MAAM;IACR;AACF;AAGO,MAAM;IACX;;;;GAIC,GACD,OAAO,aAAa,KAAa,EAAW;QAC1C,MAAM,aAAa;QACnB,OAAO,WAAW,IAAI,CAAC,UAAU,MAAM,MAAM,IAAI;IACnD;IAEA;;;;GAIC,GACD,OAAO,gBAAgB,QAAgB,EAAW;QAChD,+DAA+D;QAC/D,MAAM,gBAAgB;QACtB,OAAO,cAAc,IAAI,CAAC;IAC5B;IAEA;;;;GAIC,GACD,OAAO,iBAAiB,QAAgB,EAA0C;QAChF,MAAM,SAAmB,EAAE;QAE3B,IAAI,SAAS,MAAM,GAAG,GAAG;YACvB,OAAO,IAAI,CAAC;QACd;QAEA,IAAI,SAAS,MAAM,GAAG,KAAK;YACzB,OAAO,IAAI,CAAC;QACd;QAEA,IAAI,CAAC,QAAQ,IAAI,CAAC,WAAW;YAC3B,OAAO,IAAI,CAAC;QACd;QAEA,IAAI,CAAC,QAAQ,IAAI,CAAC,WAAW;YAC3B,OAAO,IAAI,CAAC;QACd;QAEA,IAAI,CAAC,QAAQ,IAAI,CAAC,WAAW;YAC3B,OAAO,IAAI,CAAC;QACd;QAEA,IAAI,CAAC,wCAAwC,IAAI,CAAC,WAAW;YAC3D,OAAO,IAAI,CAAC;QACd;QAEA,OAAO;YACL,SAAS,OAAO,MAAM,KAAK;YAC3B;QACF;IACF;IAEA;;;;GAIC,GACD,OAAO,cAAc,KAAa,EAAU;QAC1C,OAAO,MACJ,IAAI,GACJ,OAAO,CAAC,SAAS,IAAI,6BAA6B;SAClD,SAAS,CAAC,GAAG,OAAO,eAAe;IACxC;AACF;AAGO,MAAM;IACX;;;;GAIC,GACD,OAAO,gBAAgB,KAAa,EAAqB;QACvD,IAAI;YACF,OAAO,SAAS,WAAW,CAAC;QAC9B,EAAE,OAAO,OAAO;YACd,OAAO;QACT;IACF;IAEA;;;;GAIC,GACD,OAAO,iBAAiB,KAAa,EAAW;QAC9C,IAAI;YACF,MAAM,UAAU,SAAS,WAAW,CAAC;YACrC,OAAO,QAAQ,IAAI,KAAK;QAC1B,EAAE,OAAO,OAAO;YACd,OAAO;QACT;IACF;AACF", "debugId": null}}, {"offset": {"line": 201, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Haq%20website%20v1/haq-frontend-nextjs/src/app/auth/signup/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { useForm, SubmitHandler } from 'react-hook-form';\nimport { useRouter } from 'next/navigation';\nimport Link from 'next/link';\nimport { ValidationUtils } from '@/lib/auth';\n\n// Form data type\ninterface SignUpFormData {\n  username: string;\n  email: string;\n  password: string;\n  confirmPassword: string;\n}\n\n// API response type\ninterface SignUpResponse {\n  success: boolean;\n  message: string;\n  user?: {\n    user_id: string;\n    username: string;\n    email: string;\n    role: string;\n  };\n}\n\nexport default function SignUpPage() {\n  const router = useRouter();\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [apiError, setApiError] = useState<string | null>(null);\n  const [apiSuccess, setApiSuccess] = useState<string | null>(null);\n\n  const {\n    register,\n    handleSubmit,\n    watch,\n    formState: { errors },\n    setError,\n    clearErrors\n  } = useForm<SignUpFormData>({\n    mode: 'onBlur', // Validate on blur for better UX\n    criteriaMode: 'all' // Show all validation errors\n  });\n\n  // Watch password for confirm password validation\n  const password = watch('password');\n\n  const onSubmit: SubmitHandler<SignUpFormData> = async (data) => {\n    setIsSubmitting(true);\n    setApiError(null);\n    setApiSuccess(null);\n    clearErrors();\n\n    try {\n      // Client-side validation before API call\n      if (data.password !== data.confirmPassword) {\n        setError('confirmPassword', {\n          type: 'manual',\n          message: 'Passwords do not match'\n        });\n        setIsSubmitting(false);\n        return;\n      }\n\n      // Call registration API\n      const response = await fetch('/api/auth/register', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          username: data.username,\n          email: data.email,\n          password: data.password\n        }),\n      });\n\n      const result: SignUpResponse = await response.json();\n\n      if (result.success) {\n        setApiSuccess('Account created successfully! Redirecting...');\n        \n        // Redirect to dashboard or home page after successful registration\n        setTimeout(() => {\n          router.push('/');\n        }, 2000);\n      } else {\n        setApiError(result.message || 'Registration failed. Please try again.');\n        \n        // Handle specific field errors from server\n        if (result.message.includes('email')) {\n          setError('email', {\n            type: 'manual',\n            message: result.message\n          });\n        } else if (result.message.includes('username')) {\n          setError('username', {\n            type: 'manual',\n            message: result.message\n          });\n        }\n      }\n    } catch (error) {\n      console.error('Registration error:', error);\n      setApiError('Network error. Please check your connection and try again.');\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-background-primary flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8\">\n      <div className=\"max-w-md w-full space-y-8\">\n        {/* Header */}\n        <div className=\"text-center\">\n          <div className=\"flex justify-center mb-6\">\n            <div className=\"w-12 h-12 bg-gradient-to-br from-accent-primary to-accent-secondary rounded-xl flex items-center justify-center\">\n              <span className=\"text-white font-bold text-xl\">H</span>\n            </div>\n          </div>\n          <h2 className=\"text-3xl font-bold text-text-primary\">\n            Create your account\n          </h2>\n          <p className=\"mt-2 text-sm text-text-secondary\">\n            Join HAQ to discover and review companies\n          </p>\n        </div>\n\n        {/* Success Message */}\n        {apiSuccess && (\n          <div className=\"bg-green-50 border border-green-200 rounded-medium p-4\">\n            <div className=\"flex\">\n              <div className=\"flex-shrink-0\">\n                <svg className=\"h-5 w-5 text-green-400\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                  <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\n                </svg>\n              </div>\n              <div className=\"ml-3\">\n                <p className=\"text-sm font-medium text-green-800\">{apiSuccess}</p>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Error Message */}\n        {apiError && (\n          <div className=\"bg-red-50 border border-red-200 rounded-medium p-4\">\n            <div className=\"flex\">\n              <div className=\"flex-shrink-0\">\n                <svg className=\"h-5 w-5 text-red-400\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                  <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clipRule=\"evenodd\" />\n                </svg>\n              </div>\n              <div className=\"ml-3\">\n                <p className=\"text-sm font-medium text-red-800\">{apiError}</p>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Sign Up Form */}\n        <form className=\"mt-8 space-y-6\" onSubmit={handleSubmit(onSubmit)}>\n          <div className=\"space-y-4\">\n            {/* Username Field */}\n            <div>\n              <label htmlFor=\"username\" className=\"block text-sm font-medium text-text-primary mb-2\">\n                Username\n              </label>\n              <input\n                id=\"username\"\n                type=\"text\"\n                autoComplete=\"username\"\n                aria-invalid={errors.username ? 'true' : 'false'}\n                className={`\n                  appearance-none relative block w-full px-3 py-3 border rounded-medium placeholder-text-secondary text-text-primary \n                  focus:outline-none focus:ring-2 focus:ring-accent-primary focus:border-accent-primary focus:z-10 sm:text-sm\n                  ${errors.username \n                    ? 'border-red-300 bg-red-50' \n                    : 'border-border-primary bg-surface-primary hover:border-border-secondary'\n                  }\n                `}\n                placeholder=\"Enter your username\"\n                {...register('username', {\n                  required: 'Username is required',\n                  minLength: {\n                    value: 3,\n                    message: 'Username must be at least 3 characters long'\n                  },\n                  maxLength: {\n                    value: 50,\n                    message: 'Username must be less than 50 characters'\n                  },\n                  pattern: {\n                    value: /^[a-zA-Z0-9_]+$/,\n                    message: 'Username can only contain letters, numbers, and underscores'\n                  },\n                  validate: (value) => {\n                    if (!ValidationUtils.isValidUsername(value)) {\n                      return 'Username must be 3-50 characters and contain only letters, numbers, and underscores';\n                    }\n                    return true;\n                  }\n                })}\n              />\n              {errors.username && (\n                <p className=\"mt-1 text-sm text-red-600\" role=\"alert\">\n                  {errors.username.message}\n                </p>\n              )}\n            </div>\n\n            {/* Email Field */}\n            <div>\n              <label htmlFor=\"email\" className=\"block text-sm font-medium text-text-primary mb-2\">\n                Email address\n              </label>\n              <input\n                id=\"email\"\n                type=\"email\"\n                autoComplete=\"email\"\n                aria-invalid={errors.email ? 'true' : 'false'}\n                className={`\n                  appearance-none relative block w-full px-3 py-3 border rounded-medium placeholder-text-secondary text-text-primary \n                  focus:outline-none focus:ring-2 focus:ring-accent-primary focus:border-accent-primary focus:z-10 sm:text-sm\n                  ${errors.email \n                    ? 'border-red-300 bg-red-50' \n                    : 'border-border-primary bg-surface-primary hover:border-border-secondary'\n                  }\n                `}\n                placeholder=\"Enter your email\"\n                {...register('email', {\n                  required: 'Email is required',\n                  maxLength: {\n                    value: 255,\n                    message: 'Email must be less than 255 characters'\n                  },\n                  pattern: {\n                    value: /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/,\n                    message: 'Please enter a valid email address'\n                  },\n                  validate: (value) => {\n                    if (!ValidationUtils.isValidEmail(value)) {\n                      return 'Please enter a valid email address';\n                    }\n                    return true;\n                  }\n                })}\n              />\n              {errors.email && (\n                <p className=\"mt-1 text-sm text-red-600\" role=\"alert\">\n                  {errors.email.message}\n                </p>\n              )}\n            </div>\n\n            {/* Password Field */}\n            <div>\n              <label htmlFor=\"password\" className=\"block text-sm font-medium text-text-primary mb-2\">\n                Password\n              </label>\n              <input\n                id=\"password\"\n                type=\"password\"\n                autoComplete=\"new-password\"\n                aria-invalid={errors.password ? 'true' : 'false'}\n                className={`\n                  appearance-none relative block w-full px-3 py-3 border rounded-medium placeholder-text-secondary text-text-primary \n                  focus:outline-none focus:ring-2 focus:ring-accent-primary focus:border-accent-primary focus:z-10 sm:text-sm\n                  ${errors.password \n                    ? 'border-red-300 bg-red-50' \n                    : 'border-border-primary bg-surface-primary hover:border-border-secondary'\n                  }\n                `}\n                placeholder=\"Create a strong password\"\n                {...register('password', {\n                  required: 'Password is required',\n                  minLength: {\n                    value: 8,\n                    message: 'Password must be at least 8 characters long'\n                  },\n                  maxLength: {\n                    value: 128,\n                    message: 'Password must be less than 128 characters'\n                  },\n                  validate: (value) => {\n                    const validation = ValidationUtils.validatePassword(value);\n                    if (!validation.isValid) {\n                      return validation.errors[0]; // Show first error\n                    }\n                    return true;\n                  }\n                })}\n              />\n              {errors.password && (\n                <p className=\"mt-1 text-sm text-red-600\" role=\"alert\">\n                  {errors.password.message}\n                </p>\n              )}\n              <div className=\"mt-2 text-xs text-text-secondary\">\n                Password must contain at least 8 characters with uppercase, lowercase, number, and special character.\n              </div>\n            </div>\n\n            {/* Confirm Password Field */}\n            <div>\n              <label htmlFor=\"confirmPassword\" className=\"block text-sm font-medium text-text-primary mb-2\">\n                Confirm Password\n              </label>\n              <input\n                id=\"confirmPassword\"\n                type=\"password\"\n                autoComplete=\"new-password\"\n                aria-invalid={errors.confirmPassword ? 'true' : 'false'}\n                className={`\n                  appearance-none relative block w-full px-3 py-3 border rounded-medium placeholder-text-secondary text-text-primary \n                  focus:outline-none focus:ring-2 focus:ring-accent-primary focus:border-accent-primary focus:z-10 sm:text-sm\n                  ${errors.confirmPassword \n                    ? 'border-red-300 bg-red-50' \n                    : 'border-border-primary bg-surface-primary hover:border-border-secondary'\n                  }\n                `}\n                placeholder=\"Confirm your password\"\n                {...register('confirmPassword', {\n                  required: 'Please confirm your password',\n                  validate: (value) => {\n                    if (value !== password) {\n                      return 'Passwords do not match';\n                    }\n                    return true;\n                  }\n                })}\n              />\n              {errors.confirmPassword && (\n                <p className=\"mt-1 text-sm text-red-600\" role=\"alert\">\n                  {errors.confirmPassword.message}\n                </p>\n              )}\n            </div>\n          </div>\n\n          {/* Submit Button */}\n          <div>\n            <button\n              type=\"submit\"\n              disabled={isSubmitting}\n              className={`\n                group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-medium text-white \n                focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-accent-primary\n                ${isSubmitting \n                  ? 'bg-gray-400 cursor-not-allowed' \n                  : 'bg-accent-primary hover:bg-accent-secondary'\n                }\n              `}\n            >\n              {isSubmitting ? (\n                <>\n                  <svg className=\"animate-spin -ml-1 mr-3 h-5 w-5 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\n                    <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n                    <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                  </svg>\n                  Creating account...\n                </>\n              ) : (\n                'Create account'\n              )}\n            </button>\n          </div>\n\n          {/* Sign In Link */}\n          <div className=\"text-center\">\n            <p className=\"text-sm text-text-secondary\">\n              Already have an account?{' '}\n              <Link \n                href=\"/auth/login\" \n                className=\"font-medium text-accent-primary hover:text-accent-secondary transition-colors\"\n              >\n                Sign in\n              </Link>\n            </p>\n          </div>\n        </form>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AA4Be,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACxD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAE5D,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,KAAK,EACL,WAAW,EAAE,MAAM,EAAE,EACrB,QAAQ,EACR,WAAW,EACZ,GAAG,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAkB;QAC1B,MAAM;QACN,cAAc,MAAM,6BAA6B;IACnD;IAEA,iDAAiD;IACjD,MAAM,WAAW,MAAM;IAEvB,MAAM,WAA0C,OAAO;QACrD,gBAAgB;QAChB,YAAY;QACZ,cAAc;QACd;QAEA,IAAI;YACF,yCAAyC;YACzC,IAAI,KAAK,QAAQ,KAAK,KAAK,eAAe,EAAE;gBAC1C,SAAS,mBAAmB;oBAC1B,MAAM;oBACN,SAAS;gBACX;gBACA,gBAAgB;gBAChB;YACF;YAEA,wBAAwB;YACxB,MAAM,WAAW,MAAM,MAAM,sBAAsB;gBACjD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,UAAU,KAAK,QAAQ;oBACvB,OAAO,KAAK,KAAK;oBACjB,UAAU,KAAK,QAAQ;gBACzB;YACF;YAEA,MAAM,SAAyB,MAAM,SAAS,IAAI;YAElD,IAAI,OAAO,OAAO,EAAE;gBAClB,cAAc;gBAEd,mEAAmE;gBACnE,WAAW;oBACT,OAAO,IAAI,CAAC;gBACd,GAAG;YACL,OAAO;gBACL,YAAY,OAAO,OAAO,IAAI;gBAE9B,2CAA2C;gBAC3C,IAAI,OAAO,OAAO,CAAC,QAAQ,CAAC,UAAU;oBACpC,SAAS,SAAS;wBAChB,MAAM;wBACN,SAAS,OAAO,OAAO;oBACzB;gBACF,OAAO,IAAI,OAAO,OAAO,CAAC,QAAQ,CAAC,aAAa;oBAC9C,SAAS,YAAY;wBACnB,MAAM;wBACN,SAAS,OAAO,OAAO;oBACzB;gBACF;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;YACrC,YAAY;QACd,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,WAAU;8CAA+B;;;;;;;;;;;;;;;;sCAGnD,6LAAC;4BAAG,WAAU;sCAAuC;;;;;;sCAGrD,6LAAC;4BAAE,WAAU;sCAAmC;;;;;;;;;;;;gBAMjD,4BACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;oCAAyB,SAAQ;oCAAY,MAAK;8CAC/D,cAAA,6LAAC;wCAAK,UAAS;wCAAU,GAAE;wCAAwI,UAAS;;;;;;;;;;;;;;;;0CAGhL,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAE,WAAU;8CAAsC;;;;;;;;;;;;;;;;;;;;;;gBAO1D,0BACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;oCAAuB,SAAQ;oCAAY,MAAK;8CAC7D,cAAA,6LAAC;wCAAK,UAAS;wCAAU,GAAE;wCAA0N,UAAS;;;;;;;;;;;;;;;;0CAGlQ,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAE,WAAU;8CAAoC;;;;;;;;;;;;;;;;;;;;;;8BAOzD,6LAAC;oBAAK,WAAU;oBAAiB,UAAU,aAAa;;sCACtD,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAW,WAAU;sDAAmD;;;;;;sDAGvF,6LAAC;4CACC,IAAG;4CACH,MAAK;4CACL,cAAa;4CACb,gBAAc,OAAO,QAAQ,GAAG,SAAS;4CACzC,WAAW,CAAC;;;kBAGV,EAAE,OAAO,QAAQ,GACb,6BACA,yEACH;gBACH,CAAC;4CACD,aAAY;4CACX,GAAG,SAAS,YAAY;gDACvB,UAAU;gDACV,WAAW;oDACT,OAAO;oDACP,SAAS;gDACX;gDACA,WAAW;oDACT,OAAO;oDACP,SAAS;gDACX;gDACA,SAAS;oDACP,OAAO;oDACP,SAAS;gDACX;gDACA,UAAU,CAAC;oDACT,IAAI,CAAC,qHAAA,CAAA,kBAAe,CAAC,eAAe,CAAC,QAAQ;wDAC3C,OAAO;oDACT;oDACA,OAAO;gDACT;4CACF,EAAE;;;;;;wCAEH,OAAO,QAAQ,kBACd,6LAAC;4CAAE,WAAU;4CAA4B,MAAK;sDAC3C,OAAO,QAAQ,CAAC,OAAO;;;;;;;;;;;;8CAM9B,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAQ,WAAU;sDAAmD;;;;;;sDAGpF,6LAAC;4CACC,IAAG;4CACH,MAAK;4CACL,cAAa;4CACb,gBAAc,OAAO,KAAK,GAAG,SAAS;4CACtC,WAAW,CAAC;;;kBAGV,EAAE,OAAO,KAAK,GACV,6BACA,yEACH;gBACH,CAAC;4CACD,aAAY;4CACX,GAAG,SAAS,SAAS;gDACpB,UAAU;gDACV,WAAW;oDACT,OAAO;oDACP,SAAS;gDACX;gDACA,SAAS;oDACP,OAAO;oDACP,SAAS;gDACX;gDACA,UAAU,CAAC;oDACT,IAAI,CAAC,qHAAA,CAAA,kBAAe,CAAC,YAAY,CAAC,QAAQ;wDACxC,OAAO;oDACT;oDACA,OAAO;gDACT;4CACF,EAAE;;;;;;wCAEH,OAAO,KAAK,kBACX,6LAAC;4CAAE,WAAU;4CAA4B,MAAK;sDAC3C,OAAO,KAAK,CAAC,OAAO;;;;;;;;;;;;8CAM3B,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAW,WAAU;sDAAmD;;;;;;sDAGvF,6LAAC;4CACC,IAAG;4CACH,MAAK;4CACL,cAAa;4CACb,gBAAc,OAAO,QAAQ,GAAG,SAAS;4CACzC,WAAW,CAAC;;;kBAGV,EAAE,OAAO,QAAQ,GACb,6BACA,yEACH;gBACH,CAAC;4CACD,aAAY;4CACX,GAAG,SAAS,YAAY;gDACvB,UAAU;gDACV,WAAW;oDACT,OAAO;oDACP,SAAS;gDACX;gDACA,WAAW;oDACT,OAAO;oDACP,SAAS;gDACX;gDACA,UAAU,CAAC;oDACT,MAAM,aAAa,qHAAA,CAAA,kBAAe,CAAC,gBAAgB,CAAC;oDACpD,IAAI,CAAC,WAAW,OAAO,EAAE;wDACvB,OAAO,WAAW,MAAM,CAAC,EAAE,EAAE,mBAAmB;oDAClD;oDACA,OAAO;gDACT;4CACF,EAAE;;;;;;wCAEH,OAAO,QAAQ,kBACd,6LAAC;4CAAE,WAAU;4CAA4B,MAAK;sDAC3C,OAAO,QAAQ,CAAC,OAAO;;;;;;sDAG5B,6LAAC;4CAAI,WAAU;sDAAmC;;;;;;;;;;;;8CAMpD,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAkB,WAAU;sDAAmD;;;;;;sDAG9F,6LAAC;4CACC,IAAG;4CACH,MAAK;4CACL,cAAa;4CACb,gBAAc,OAAO,eAAe,GAAG,SAAS;4CAChD,WAAW,CAAC;;;kBAGV,EAAE,OAAO,eAAe,GACpB,6BACA,yEACH;gBACH,CAAC;4CACD,aAAY;4CACX,GAAG,SAAS,mBAAmB;gDAC9B,UAAU;gDACV,UAAU,CAAC;oDACT,IAAI,UAAU,UAAU;wDACtB,OAAO;oDACT;oDACA,OAAO;gDACT;4CACF,EAAE;;;;;;wCAEH,OAAO,eAAe,kBACrB,6LAAC;4CAAE,WAAU;4CAA4B,MAAK;sDAC3C,OAAO,eAAe,CAAC,OAAO;;;;;;;;;;;;;;;;;;sCAOvC,6LAAC;sCACC,cAAA,6LAAC;gCACC,MAAK;gCACL,UAAU;gCACV,WAAW,CAAC;;;gBAGV,EAAE,eACE,mCACA,8CACH;cACH,CAAC;0CAEA,6BACC;;sDACE,6LAAC;4CAAI,WAAU;4CAA6C,OAAM;4CAA6B,MAAK;4CAAO,SAAQ;;8DACjH,6LAAC;oDAAO,WAAU;oDAAa,IAAG;oDAAK,IAAG;oDAAK,GAAE;oDAAK,QAAO;oDAAe,aAAY;;;;;;8DACxF,6LAAC;oDAAK,WAAU;oDAAa,MAAK;oDAAe,GAAE;;;;;;;;;;;;wCAC/C;;mDAIR;;;;;;;;;;;sCAMN,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,WAAU;;oCAA8B;oCAChB;kDACzB,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;GAtWwB;;QACP,qIAAA,CAAA,YAAS;QAYpB,iKAAA,CAAA,UAAO;;;KAbW", "debugId": null}}]}